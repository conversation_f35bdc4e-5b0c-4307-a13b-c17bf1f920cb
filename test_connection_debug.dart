import 'lib/service/serial_gps_service.dart';

/// 调试GPS连接问题的测试程序
void main() async {
  print('🔍 GPS连接问题调试程序');
  print('='*50);
  
  final gpsService = SerialGPSService.instance;
  
  try {
    // 1. 检查初始状态
    print('\n📊 初始状态检查:');
    gpsService.debugConnectionStatus();
    
    // 2. 尝试连接
    print('\n🔌 开始连接GPS模块...');
    final connected = await gpsService.connect();
    
    print('\n📊 连接后状态检查:');
    print('connect()返回值: $connected');
    print('isConnected属性: ${gpsService.isConnected}');
    gpsService.debugConnectionStatus();
    
    if (connected) {
      print('\n✅ 连接成功，等待观察...');
      
      // 3. 等待一段时间观察状态变化
      for (int i = 1; i <= 10; i++) {
        await Future.delayed(Duration(seconds: 1));
        print('\n⏱️ 第${i}秒状态:');
        print('isConnected: ${gpsService.isConnected}');
        
        if (!gpsService.isConnected) {
          print('❌ 连接在第${i}秒时断开！');
          gpsService.debugConnectionStatus();
          break;
        }
      }
      
      // 4. 如果连接仍然正常，尝试手动初始化
      if (gpsService.isConnected) {
        print('\n🔧 连接状态正常，尝试手动初始化...');
        
        // 直接调用初始化方法
        final initSuccess = await gpsService.initializeGPSModule();
        print('初始化结果: $initSuccess');
        
        if (initSuccess) {
          print('✅ 初始化成功！');
          
          // 观察定时器工作
          print('\n⏰ 观察定时器工作（30秒）...');
          await Future.delayed(Duration(seconds: 30));
          
        } else {
          print('❌ 初始化失败');
          gpsService.debugConnectionStatus();
        }
      }
      
    } else {
      print('\n❌ 连接失败');
      gpsService.debugConnectionStatus();
    }
    
  } catch (e) {
    print('\n💥 测试过程中发生异常: $e');
    gpsService.debugConnectionStatus();
  } finally {
    // 清理资源
    print('\n🔌 断开连接...');
    await gpsService.disconnect();
    
    print('\n📊 断开后状态:');
    gpsService.debugConnectionStatus();
  }
  
  print('\n' + '='*50);
  print('🏁 调试测试完成');
  print('='*50);
}

/// 额外的串口设备检查
Future<void> checkSerialDevice() async {
  print('\n🔍 检查串口设备:');
  
  try {
    // 检查设备文件是否存在
    final deviceFile = File('/dev/ttyS7');
    final exists = await deviceFile.exists();
    print('设备文件存在: $exists');
    
    if (exists) {
      // 检查权限
      final process = await Process.start('ls', ['-l', '/dev/ttyS7']);
      final output = await process.stdout.transform(utf8.decoder).join();
      print('设备权限: $output');
      
      // 检查是否被其他进程占用
      final lsofProcess = await Process.start('lsof', ['/dev/ttyS7']);
      final lsofOutput = await lsofProcess.stdout.transform(utf8.decoder).join();
      if (lsofOutput.isNotEmpty) {
        print('设备被占用: $lsofOutput');
      } else {
        print('设备未被占用');
      }
    }
    
  } catch (e) {
    print('检查串口设备时出错: $e');
  }
}
