import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../service/ollama_vision_service.dart';
import '../service/openrouter_service.dart';
import '../service/settings_service.dart';
import '../theme/industrial_theme.dart';
import '../widgets/industrial_widgets.dart';
import '../utils/kiosk_manager.dart';

/// 系统设置页面
/// 提供工业风格的设置界面，用于配置图片识别服务
class SystemSettingsPage extends StatefulWidget {
  const SystemSettingsPage({Key? key}) : super(key: key);

  @override
  _SystemSettingsPageState createState() => _SystemSettingsPageState();
}

class _SystemSettingsPageState extends State<SystemSettingsPage> {
  final OllamaVisionService _visionService = OllamaVisionService.instance;
  final SettingsService _settingsService = SettingsService.instance;
  final KioskManager _kioskManager = KioskManager.instance;
  final ImagePicker _picker = ImagePicker();

  // 控制器
  late TextEditingController _serverUrlController;
  late TextEditingController _apiKeyController;
  late TextEditingController _modelController;

  // 新增设置状态
  OCREngine _selectedOCREngine = OCREngine.openrouter;
  String _selectedOpenRouterVisionModel = 'qwen/qwen2.5-vl-72b-instruct:free';
  String _selectedOpenRouterChatModel = 'deepseek/deepseek-chat-v3-0324:free';
  bool _kioskModeEnabled = true;

  // 状态
  bool _isLoading = false;
  bool _isConnected = false;
  String? _connectionError;
  Map<String, dynamic>? _serviceStatus;

  // 测试相关状态
  bool _isTesting = false;
  String? _testResult;
  File? _testImage;
  bool _isTestingOpenRouter = false;

  @override
  void initState() {
    super.initState();
    // 先加载设置，再初始化控制器
    _loadSettings();
    _initControllers();
    _checkServiceStatus();
    _checkKioskModeStatus();
  }

  void _loadSettings() {
    _selectedOCREngine = _settingsService.ocrEngine;
    _selectedOpenRouterVisionModel = _settingsService.openrouterVisionModel;
    _selectedOpenRouterChatModel = _settingsService.openrouterChatModel;
    _kioskModeEnabled = _settingsService.kioskModeEnabled;

    // 确保在setState中更新UI
    if (mounted) {
      setState(() {});
    }
  }

  String _getCurrentMainModel() {
    switch (_selectedOCREngine) {
      case OCREngine.openrouter:
        return _selectedOpenRouterVisionModel.split('/').last.split(':').first;
      case OCREngine.ollama:
        return _settingsService.ollamaModel;
      case OCREngine.local:
        return 'ML Kit OCR';
    }
  }

  void _initControllers() {
    // 确保从最新的设置中初始化控制器
    _serverUrlController = TextEditingController(text: _settingsService.ollamaServerUrl);
    _apiKeyController = TextEditingController(text: _settingsService.ollamaApiKey);
    _modelController = TextEditingController(text: _settingsService.ollamaModel);

    print('初始化控制器 - 服务器URL: ${_settingsService.ollamaServerUrl}');
    print('初始化控制器 - API密钥: ${_settingsService.ollamaApiKey}');
    print('初始化控制器 - 模型: ${_settingsService.ollamaModel}');
  }

  /// 检查当前Kiosk模式状态
  Future<void> _checkKioskModeStatus() async {
    try {
      final isKioskActive = await _kioskManager.checkKioskModeStatus();
      if (mounted && isKioskActive != _kioskModeEnabled) {
        setState(() {
          _kioskModeEnabled = isKioskActive;
        });
      }
    } catch (e) {
      print('检查Kiosk模式状态失败: $e');
    }
  }

  @override
  void dispose() {
    _serverUrlController.dispose();
    _apiKeyController.dispose();
    _modelController.dispose();
    super.dispose();
  }

  /// 重新加载设置（当页面恢复时调用）
  void _reloadSettings() {
    setState(() {
      _loadSettings();

      // 更新控制器的文本
      _serverUrlController.text = _settingsService.ollamaServerUrl;
      _apiKeyController.text = _settingsService.ollamaApiKey;
      _modelController.text = _settingsService.ollamaModel;

      print('重新加载设置 - 服务器URL: ${_settingsService.ollamaServerUrl}');
      print('重新加载设置 - API密钥: ${_settingsService.ollamaApiKey}');
      print('重新加载设置 - 模型: ${_settingsService.ollamaModel}');
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面依赖发生变化时重新加载设置
    _reloadSettings();
  }

  /// 检查服务状态
  Future<void> _checkServiceStatus() async {
    setState(() {
      _isLoading = true;
      _connectionError = null;
    });

    try {
      final status = await _visionService.getServiceStatus();
      setState(() {
        _serviceStatus = status;
        _isConnected = status['connected'] ?? false;
        _connectionError = status['error'];
      });
    } catch (e) {
      setState(() {
        _connectionError = e.toString();
        _isConnected = false;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 保存所有设置
      _settingsService.ocrEngine = _selectedOCREngine;
      _settingsService.openrouterVisionModel = _selectedOpenRouterVisionModel;
      _settingsService.openrouterChatModel = _selectedOpenRouterChatModel;
      _settingsService.kioskModeEnabled = _kioskModeEnabled;
      _settingsService.ollamaServerUrl = _serverUrlController.text.trim();
      _settingsService.ollamaApiKey = _apiKeyController.text.trim();
      _settingsService.ollamaModel = _modelController.text.trim();

      // 同时更新Ollama服务配置
      _visionService.setServerUrl(_serverUrlController.text.trim());
      _visionService.setApiKey(_apiKeyController.text.trim());
      _visionService.setModel(_modelController.text.trim());

      // 测试连接
      await _checkServiceStatus();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('设置已保存'),
            backgroundColor: IndustrialTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $e'),
            backgroundColor: IndustrialTheme.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 重置为默认设置
  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('重置设置'),
        content: Text('确定要重置为默认设置吗？这将清除所有自定义配置。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _visionService.resetToDefaults();
              _initControllers();
              _checkServiceStatus();
              setState(() {});
            },
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 测试图片识别功能
  Future<void> _testImageRecognition() async {
    setState(() {
      _isTesting = true;
      _testResult = null;
      _testImage = null;
    });

    try {
      // 1. 拍照
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('未拍摄照片')),
          );
        }
        return;
      }

      final imageFile = File(photo.path);
      setState(() {
        _testImage = imageFile;
      });

      // 2. 识别图片
      final result = await _visionService.recognizeNumbers(imageFile);

      setState(() {
        _testResult = result;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('识别完成: $result'),
            backgroundColor: IndustrialTheme.successGreen,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _testResult = '识别失败: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('测试失败: $e'),
            backgroundColor: IndustrialTheme.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: Text(
          '系统设置',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: IndustrialTheme.primaryBlue,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(IndustrialStyles.spacingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 页面标题
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(Icons.settings, color: IndustrialTheme.primaryBlue, size: 32),
                  SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '图片识别服务配置',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: IndustrialTheme.textPrimary,
                        ),
                      ),
                      Text(
                        '当前引擎：${_selectedOCREngine.displayName} | 主要模型：${_getCurrentMainModel()}',
                        style: TextStyle(
                          fontSize: 14,
                          color: IndustrialTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: IndustrialStyles.spacingL),
            
            // 服务状态卡片
            _buildStatusCard(),
            
            const SizedBox(height: IndustrialStyles.spacingL),
            
            // OCR引擎选择
            _buildOCREngineSelection(),

            const SizedBox(height: IndustrialStyles.spacingL),

            // OpenRouter配置
            _buildOpenRouterConfiguration(),

            const SizedBox(height: IndustrialStyles.spacingL),

            // Ollama配置表单
            _buildOllamaConfigurationForm(),

            const SizedBox(height: IndustrialStyles.spacingL),

            // Kiosk模式设置
            _buildKioskModeSettings(),

            const SizedBox(height: IndustrialStyles.spacingL),

            // 测试区域
            _buildTestSection(),

            const SizedBox(height: IndustrialStyles.spacingL),

            // 操作按钮
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard() {
    return IndustrialInfoPanel(
      title: '服务状态',
      icon: Icons.cloud,
      headerColor: _isConnected 
          ? IndustrialTheme.successGreen 
          : IndustrialTheme.errorRed,
      children: [
        IndustrialDataRow(
          label: '连接状态',
          value: _isLoading 
              ? '检测中...' 
              : (_isConnected ? '已连接' : '未连接'),
          icon: _isLoading 
              ? Icons.refresh 
              : (_isConnected ? Icons.check_circle : Icons.error),
          valueColor: _isLoading 
              ? IndustrialTheme.warningAmber 
              : (_isConnected ? IndustrialTheme.successGreen : IndustrialTheme.errorRed),
        ),
        IndustrialDataRow(
          label: '当前引擎',
          value: _selectedOCREngine.displayName,
          icon: Icons.psychology,
          valueColor: IndustrialTheme.primaryBlue,
        ),
        IndustrialDataRow(
          label: '主要模型',
          value: _getCurrentMainModel(),
          icon: Icons.model_training,
        ),
        if (_selectedOCREngine == OCREngine.ollama && _serviceStatus != null) ...[
          IndustrialDataRow(
            label: 'Ollama地址',
            value: _serviceStatus!['serverUrl'] ?? '-',
            icon: Icons.link,
          ),
          IndustrialDataRow(
            label: '最后检查',
            value: _formatDateTime(_serviceStatus!['lastCheck']),
            icon: Icons.access_time,
          ),
        ],
        if (_selectedOCREngine == OCREngine.openrouter) ...[
          IndustrialDataRow(
            label: 'OpenRouter状态',
            value: '云端服务',
            icon: Icons.cloud,
            valueColor: IndustrialTheme.successGreen,
          ),
        ],
        if (_selectedOCREngine == OCREngine.local) ...[
          IndustrialDataRow(
            label: '本地OCR状态',
            value: '设备内置',
            icon: Icons.phone_android,
            valueColor: IndustrialTheme.successGreen,
          ),
        ],
        if (_connectionError != null)
          IndustrialDataRow(
            label: '错误信息',
            value: _connectionError!,
            icon: Icons.error_outline,
            valueColor: IndustrialTheme.errorRed,
          ),
      ],
    );
  }

  /// 构建OCR引擎选择
  Widget _buildOCREngineSelection() {
    return IndustrialInfoPanel(
      title: 'OCR识别引擎',
      icon: Icons.psychology,
      headerColor: IndustrialTheme.getModuleColor('engine'),
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '选择OCR识别引擎，失败时会自动降级到下一个引擎',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),
        ...OCREngine.values.map((engine) =>
          RadioListTile<OCREngine>(
            title: Text(engine.displayName),
            subtitle: Text(_getEngineDescription(engine)),
            value: engine,
            groupValue: _selectedOCREngine,
            onChanged: (value) {
              setState(() {
                _selectedOCREngine = value!;
              });
            },
          ),
        ).toList(),
      ],
    );
  }

  String _getEngineDescription(OCREngine engine) {
    switch (engine) {
      case OCREngine.openrouter:
        return '使用OpenRouter云端视觉模型，准确率高';
      case OCREngine.ollama:
        return '使用本地Ollama服务，数据安全';
      case OCREngine.local:
        return '使用设备本地OCR，无需网络';
    }
  }

  /// 构建OpenRouter配置
  Widget _buildOpenRouterConfiguration() {
    return IndustrialInfoPanel(
      title: 'OpenRouter配置',
      icon: Icons.cloud,
      headerColor: IndustrialTheme.getModuleColor('openrouter'),
      children: [
        // 视觉模型选择
        Text('视觉识别模型', style: TextStyle(fontWeight: FontWeight.bold)),
        SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedOpenRouterVisionModel,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.visibility),
          ),
          items: OpenRouterService.getAvailableVisionModels().map((model) =>
            DropdownMenuItem(value: model, child: Text(model))
          ).toList(),
          onChanged: (value) {
            setState(() {
              _selectedOpenRouterVisionModel = value!;
            });
          },
        ),
        SizedBox(height: 16),

        // 聊天模型选择
        Text('大语言模型', style: TextStyle(fontWeight: FontWeight.bold)),
        SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedOpenRouterChatModel,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.chat),
          ),
          items: OpenRouterService.getAvailableChatModels().map((model) =>
            DropdownMenuItem(value: model, child: Text(model))
          ).toList(),
          onChanged: (value) {
            setState(() {
              _selectedOpenRouterChatModel = value!;
            });
          },
        ),
        SizedBox(height: 16),

        // 测试按钮
        ElevatedButton.icon(
          onPressed: _isTestingOpenRouter ? null : _testOpenRouterConnection,
          icon: _isTestingOpenRouter
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Icon(Icons.science),
          label: Text(_isTestingOpenRouter ? '测试中...' : '测试OpenRouter连接'),
          style: ElevatedButton.styleFrom(
            backgroundColor: IndustrialTheme.warningAmber,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// 测试OpenRouter连接
  Future<void> _testOpenRouterConnection() async {
    setState(() {
      _isTestingOpenRouter = true;
    });

    try {
      final success = await OpenRouterService.testConnection(
        chatModel: _selectedOpenRouterChatModel,
        visionModel: _selectedOpenRouterVisionModel,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'OpenRouter连接成功' : 'OpenRouter连接失败'),
            backgroundColor: success ? IndustrialTheme.successGreen : IndustrialTheme.errorRed,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('OpenRouter测试失败: $e'),
            backgroundColor: IndustrialTheme.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isTestingOpenRouter = false;
      });
    }
  }

  /// 构建Ollama配置表单
  Widget _buildOllamaConfigurationForm() {
    return IndustrialInfoPanel(
      title: 'Ollama服务配置',
      icon: Icons.dns,
      headerColor: IndustrialTheme.getModuleColor('ollama'),
      children: [
        _buildTextField(
          controller: _serverUrlController,
          label: '服务端地址',
          hint: 'https://cup-ollama.ailer.ltd',
          icon: Icons.dns,
        ),
        const SizedBox(height: IndustrialStyles.spacingM),
        _buildTextField(
          controller: _apiKeyController,
          label: 'API密钥',
          hint: '请输入API密钥',
          icon: Icons.key,
          obscureText: true,
        ),
        const SizedBox(height: IndustrialStyles.spacingM),
        _buildTextField(
          controller: _modelController,
          label: '模型名称',
          hint: 'qwen2.5vl:7b',
          icon: Icons.psychology,
        ),
      ],
    );
  }

  /// 构建Kiosk模式设置
  Widget _buildKioskModeSettings() {
    return IndustrialInfoPanel(
      title: 'Kiosk模式设置',
      icon: Icons.lock,
      headerColor: IndustrialTheme.getModuleColor('security'),
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.warning_amber, color: Colors.orange[600], size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Kiosk模式将隐藏系统导航栏和状态栏，需要管理员权限退出',
                  style: TextStyle(
                    color: Colors.orange[700],
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),
        SwitchListTile(
          title: Text('启用Kiosk模式'),
          subtitle: Text(_kioskModeEnabled
              ? '系统UI已隐藏，应用处于全屏锁定状态'
              : '系统UI正常显示，用户可以访问系统功能'),
          value: _kioskModeEnabled,
          onChanged: _isLoading ? null : _toggleKioskMode,
        ),
        if (_kioskModeEnabled) ...[
          SizedBox(height: 8),
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '退出Kiosk模式：连续点击屏幕右上角7次',
              style: TextStyle(
                color: Colors.green[700],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 切换Kiosk模式
  Future<void> _toggleKioskMode(bool value) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final kioskManager = _kioskManager;
      bool success = false;

      if (value) {
        // 启用Kiosk模式
        success = await kioskManager.enableKioskMode();
        if (success) {
          // 启动锁定任务模式（静默版本）
          await kioskManager.startLockTaskSilent();
        }
      } else {
        // 禁用Kiosk模式
        success = await kioskManager.disableKioskMode();
        if (success) {
          // 停止锁定任务模式
          await kioskManager.stopLockTask();
        }
      }

      if (success) {
        setState(() {
          _kioskModeEnabled = value;
        });

        // 立即保存kiosk模式设置到持久化存储
        try {
          _settingsService.kioskModeEnabled = value;
          debugPrint('Kiosk模式设置已保存: $value');
        } catch (e) {
          debugPrint('保存Kiosk模式设置失败: $e');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(value ? 'Kiosk模式已启用' : 'Kiosk模式已禁用'),
              backgroundColor: IndustrialTheme.successGreen,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(value ? '启用Kiosk模式失败' : '禁用Kiosk模式失败'),
              backgroundColor: IndustrialTheme.errorRed,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Kiosk模式切换失败: $e'),
            backgroundColor: IndustrialTheme.errorRed,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 构建测试区域
  Widget _buildTestSection() {
    return IndustrialInfoPanel(
      title: '功能测试',
      icon: Icons.camera_alt,
      headerColor: IndustrialTheme.warningAmber,
      children: [
        // 测试说明
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  '点击测试按钮拍照，验证图片识别功能是否正常工作',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: IndustrialStyles.spacingM),

        // 测试按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isTesting ? null : _testImageRecognition,
            icon: _isTesting
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Icon(Icons.camera_alt),
            label: Text(_isTesting ? '测试中...' : '拍照测试识别'),
            style: ElevatedButton.styleFrom(
              backgroundColor: IndustrialTheme.warningAmber,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),

        // 测试结果显示
        if (_testImage != null || _testResult != null) ...[
          const SizedBox(height: IndustrialStyles.spacingM),
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '测试结果',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: IndustrialTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),

                // 显示测试图片
                if (_testImage != null) ...[
                  Container(
                    height: 120,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(
                        _testImage!,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],

                // 显示识别结果
                IndustrialDataRow(
                  label: '识别结果',
                  value: _testResult ?? '处理中...',
                  icon: Icons.text_fields,
                  valueColor: _testResult != null && !_testResult!.startsWith('识别失败')
                      ? IndustrialTheme.successGreen
                      : IndustrialTheme.errorRed,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool obscureText = false,
  }) {
    return TextField(
      controller: controller,
      obscureText: obscureText,
      style: TextStyle(
        color: IndustrialTheme.textPrimary,
        fontSize: 16,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: IndustrialTheme.primaryBlue),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: IndustrialTheme.borderGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: IndustrialTheme.primaryBlue, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
        labelStyle: TextStyle(color: IndustrialTheme.textSecondary),
        hintStyle: TextStyle(color: IndustrialTheme.textDisabled),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _checkServiceStatus,
            icon: _isLoading 
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Icon(Icons.refresh),
            label: Text(_isLoading ? '检测中...' : '测试连接'),
            style: ElevatedButton.styleFrom(
              backgroundColor: IndustrialTheme.warningAmber,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: IndustrialStyles.spacingM),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _saveSettings,
            icon: Icon(Icons.save),
            label: Text('保存设置'),
            style: ElevatedButton.styleFrom(
              backgroundColor: IndustrialTheme.successGreen,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: IndustrialStyles.spacingM),
        ElevatedButton.icon(
          onPressed: _isLoading ? null : _resetToDefaults,
          icon: Icon(Icons.restore),
          label: Text('重置'),
          style: ElevatedButton.styleFrom(
            backgroundColor: IndustrialTheme.textSecondary,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(String? isoString) {
    if (isoString == null) return '-';
    try {
      final dateTime = DateTime.parse(isoString);
      return '${dateTime.hour.toString().padLeft(2, '0')}:'
             '${dateTime.minute.toString().padLeft(2, '0')}:'
             '${dateTime.second.toString().padLeft(2, '0')}';
    } catch (e) {
      return '-';
    }
  }
}
