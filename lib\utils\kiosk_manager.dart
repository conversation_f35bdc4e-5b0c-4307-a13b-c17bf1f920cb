import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

/// Kiosk模式管理器
/// 负责管理应用的kiosk模式状态和相关功能
class KioskManager {
  static const MethodChannel _channel = MethodChannel('com.example.a325_lya_1000/kiosk');
  static KioskManager? _instance;
  
  bool _isKioskModeEnabled = false;
  bool _isAdminExitEnabled = false;
  
  // 管理员退出手势相关
  int _tapCount = 0;
  DateTime? _lastTapTime;
  static const int _requiredTaps = 7; // 需要连续点击7次
  static const Duration _tapTimeout = Duration(seconds: 3); // 3秒内完成
  
  KioskManager._();
  
  static KioskManager get instance {
    _instance ??= KioskManager._();
    return _instance!;
  }
  
  /// 获取kiosk模式状态
  bool get isKioskModeEnabled => _isKioskModeEnabled;
  
  /// 获取管理员退出是否启用
  bool get isAdminExitEnabled => _isAdminExitEnabled;
  
  /// 启用kiosk模式
  Future<bool> enableKioskMode() async {
    try {
      final result = await _channel.invokeMethod('enableKioskMode');
      _isKioskModeEnabled = result == true;
      
      // 隐藏系统UI
      await _hideSystemUI();
      
      return _isKioskModeEnabled;
    } catch (e) {
      debugPrint('启用kiosk模式失败: $e');
      return false;
    }
  }
  
  /// 禁用kiosk模式
  Future<bool> disableKioskMode() async {
    try {
      final result = await _channel.invokeMethod('disableKioskMode');
      _isKioskModeEnabled = !(result == true);
      
      // 显示系统UI
      await _showSystemUI();
      
      return !_isKioskModeEnabled;
    } catch (e) {
      debugPrint('禁用kiosk模式失败: $e');
      return false;
    }
  }
  
  /// 检查kiosk模式状态
  Future<bool> checkKioskModeStatus() async {
    try {
      final result = await _channel.invokeMethod('isKioskModeEnabled');
      _isKioskModeEnabled = result == true;
      return _isKioskModeEnabled;
    } catch (e) {
      debugPrint('检查kiosk模式状态失败: $e');
      return false;
    }
  }
  
  /// 启动锁定任务模式
  Future<bool> startLockTask() async {
    try {
      final result = await _channel.invokeMethod('startLockTask');
      return result == true;
    } catch (e) {
      debugPrint('启动锁定任务模式失败: $e');
      return false;
    }
  }

  /// 启动锁定任务模式（无提示版本）
  Future<bool> startLockTaskSilent() async {
    try {
      final result = await _channel.invokeMethod('startLockTaskSilent');
      return result == true;
    } catch (e) {
      debugPrint('启动静默锁定任务模式失败: $e');
      return false;
    }
  }
  
  /// 停止锁定任务模式
  Future<bool> stopLockTask() async {
    try {
      final result = await _channel.invokeMethod('stopLockTask');
      return result == true;
    } catch (e) {
      debugPrint('停止锁定任务模式失败: $e');
      return false;
    }
  }

  /// 启用设备管理员
  Future<bool> enableDeviceAdmin() async {
    try {
      final result = await _channel.invokeMethod('enableDeviceAdmin');
      return result == true;
    } catch (e) {
      debugPrint('启用设备管理员失败: $e');
      return false;
    }
  }

  /// 检查设备管理员是否启用
  Future<bool> isDeviceAdminEnabled() async {
    try {
      final result = await _channel.invokeMethod('isDeviceAdminEnabled');
      return result == true;
    } catch (e) {
      debugPrint('检查设备管理员状态失败: $e');
      return false;
    }
  }

  /// 禁用设备管理员
  Future<bool> disableDeviceAdmin() async {
    try {
      final result = await _channel.invokeMethod('disableDeviceAdmin');
      return result == true;
    } catch (e) {
      debugPrint('禁用设备管理员失败: $e');
      return false;
    }
  }

  /// 关机设备
  Future<bool> shutdownDevice() async {
    try {
      final result = await _channel.invokeMethod('shutdownDevice');
      return result == true;
    } catch (e) {
      debugPrint('关机设备失败: $e');
      return false;
    }
  }

  /// 重启设备
  Future<bool> rebootDevice() async {
    try {
      final result = await _channel.invokeMethod('rebootDevice');
      return result == true;
    } catch (e) {
      debugPrint('重启设备失败: $e');
      return false;
    }
  }
  
  /// 隐藏系统UI
  Future<void> _hideSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );
  }
  
  /// 显示系统UI
  Future<void> _showSystemUI() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values,
    );
  }
  
  /// 处理管理员退出手势（连续点击）
  bool handleAdminExitGesture() {
    final now = DateTime.now();
    
    // 检查是否在超时时间内
    if (_lastTapTime != null && now.difference(_lastTapTime!) > _tapTimeout) {
      _tapCount = 0;
    }
    
    _tapCount++;
    _lastTapTime = now;
    
    debugPrint('管理员退出手势: $_tapCount/$_requiredTaps');
    
    if (_tapCount >= _requiredTaps) {
      _tapCount = 0;
      _lastTapTime = null;
      return true; // 触发管理员退出
    }
    
    return false;
  }
  
  /// 启用管理员退出功能
  void enableAdminExit() {
    _isAdminExitEnabled = true;
  }
  
  /// 禁用管理员退出功能
  void disableAdminExit() {
    _isAdminExitEnabled = false;
    _tapCount = 0;
    _lastTapTime = null;
  }
  
  /// 重置管理员退出手势计数
  void resetAdminExitGesture() {
    _tapCount = 0;
    _lastTapTime = null;
  }
  
  /// 显示管理员退出确认对话框
  Future<bool> showAdminExitDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('管理员确认'),
          content: const Text('您确定要退出kiosk模式吗？\n这将允许用户访问系统功能。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确认退出'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示管理员密码输入对话框
  Future<bool> showAdminPasswordDialog(BuildContext context) async {
    const String adminPassword = "LYA1000ADMIN"; // 管理员密码
    String inputPassword = "";

    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('管理员验证'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
              const Text('请输入管理员密码以退出kiosk模式：'),
              const SizedBox(height: 16),
              TextField(
                obscureText: true,
                autofocus: true,
                decoration: const InputDecoration(
                  labelText: '管理员密码',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  inputPassword = value;
                },
                onSubmitted: (value) {
                  if (value == adminPassword) {
                    Navigator.of(context).pop(true);
                  } else {
                    // 密码错误，显示提示
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('密码错误'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
              ),
            ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (inputPassword == adminPassword) {
                  Navigator.of(context).pop(true);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('密码错误'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: const Text('确认'),
            ),
          ],
        );
      },
    ) ?? false;
  }
  
  /// 初始化kiosk模式
  Future<void> initialize() async {
    // 首先检查系统当前的kiosk状态
    await checkKioskModeStatus();

    // 读取用户保存的设置并应用
    await _applyUserSettings();

    // 延迟启动锁定任务模式以避免系统提示
    Future.delayed(const Duration(milliseconds: 1500), () async {
      await startLockTaskSilent();
    });

    enableAdminExit();
  }

  /// 应用用户保存的设置
  Future<void> _applyUserSettings() async {
    try {
      // 直接使用GetStorage读取用户设置，避免循环依赖
      final storage = GetStorage();
      final userKioskSetting = storage.read('kiosk_mode_enabled') ?? true;

      debugPrint('用户kiosk设置: $userKioskSetting, 当前系统状态: $_isKioskModeEnabled');

      // 如果用户设置与当前系统状态不一致，则应用用户设置
      if (userKioskSetting != _isKioskModeEnabled) {
        debugPrint('应用用户kiosk设置: $userKioskSetting');
        if (userKioskSetting) {
          await enableKioskMode();
        } else {
          await disableKioskMode();
        }
      }
    } catch (e) {
      debugPrint('应用用户设置失败: $e');
      // 如果读取设置失败，使用默认行为（启用kiosk模式）
      if (!_isKioskModeEnabled) {
        await enableKioskMode();
      }
    }
  }
  
  /// 清理资源
  void dispose() {
    _isAdminExitEnabled = false;
    _tapCount = 0;
    _lastTapTime = null;
  }
}
