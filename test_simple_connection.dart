import 'lib/service/serial_gps_service.dart';

/// 简单的GPS连接测试
void main() async {
  print('🔍 简单GPS连接测试');
  print('='*40);
  
  final gpsService = SerialGPSService.instance;
  
  try {
    print('🔌 尝试连接GPS...');
    final connected = await gpsService.connect();
    
    print('连接结果: $connected');
    print('连接状态: ${gpsService.isConnected}');
    
    if (connected && gpsService.isConnected) {
      print('✅ 连接成功！');
      
      // 等待5秒观察状态
      for (int i = 1; i <= 5; i++) {
        await Future.delayed(Duration(seconds: 1));
        print('第${i}秒 - 连接状态: ${gpsService.isConnected}');
      }
      
      // 如果连接仍然正常，尝试发送指令
      if (gpsService.isConnected) {
        print('\n📡 尝试发送测试指令...');
        final cmdSuccess = await gpsService.sendNMEACommand('PAIR650,10*14');
        print('指令发送结果: $cmdSuccess');
      }
      
    } else {
      print('❌ 连接失败');
    }
    
  } catch (e) {
    print('❌ 异常: $e');
  } finally {
    await gpsService.disconnect();
    print('🔌 已断开连接');
  }
}
